import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Subject, takeUntil, forkJoin, catchError, of } from 'rxjs';

import { RequestService } from '../../../../core/services/request.service';
import { AuthService } from '../../../../core/services/auth.service';
import { ReportingService } from '../../../../core/services/reporting.service';
import { RequestDto, RequestStatus, RequestType, PaginationParams } from '../../../../core/models';
import { ApproveRejectStepDto } from '../../../../core/models/request.models';

interface TeamMetrics {
  totalTeamMembers: number;
  pendingApprovals: number;
  approvedThisMonth: number;
  rejectedThisMonth: number;
  averageApprovalTime: number;
}

@Component({
  selector: 'app-manager-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatChipsModule,
    MatProgressBarModule,
    MatTabsModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="manager-dashboard">
      <div class="dashboard-header">
        <h1>Manager Dashboard</h1>
        <p>Review and approve your team's requests</p>
        <div class="header-actions">
          <button mat-raised-button color="primary" routerLink="/requests/approval">
            <mat-icon>approval</mat-icon>
            View All Pending Approvals
          </button>
        </div>
      </div>

      <!-- Key Metrics -->
      <div class="metrics-grid" *ngIf="!loadingMetrics; else metricsLoading">
        <mat-card class="metric-card pending">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>pending_actions</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{teamMetrics.pendingApprovals}}</h3>
                <p>Pending Approvals</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card team">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>group</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{teamMetrics.totalTeamMembers}}</h3>
                <p>Team Members</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card approved">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>check_circle</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{teamMetrics.approvedThisMonth}}</h3>
                <p>Approved This Month</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card time">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>schedule</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{teamMetrics.averageApprovalTime}}h</h3>
                <p>Avg. Approval Time</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <ng-template #metricsLoading>
        <div class="metrics-grid">
          <mat-card class="metric-card loading" *ngFor="let i of [1,2,3,4]">
            <mat-card-content>
              <div class="metric">
                <div class="metric-icon">
                  <mat-progress-spinner diameter="40" mode="indeterminate"></mat-progress-spinner>
                </div>
                <div class="metric-info">
                  <h3>...</h3>
                  <p>Loading...</p>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </ng-template>

      <!-- Main Content Tabs -->
      <mat-card class="content-card">
        <mat-tab-group>
          <!-- Pending Approvals -->
          <mat-tab label="Team Requests ({{pendingRequests.length}})">
            <div class="tab-content">
              <div class="tab-header">
                <h3>Team Requests Awaiting Your Approval</h3>
                <div class="header-info">
                  <mat-icon>info</mat-icon>
                  <span>Showing requests from your team members only</span>
                </div>
              </div>

              <div *ngIf="loadingRequests" class="loading-container">
                <mat-progress-spinner diameter="50" mode="indeterminate"></mat-progress-spinner>
                <p>Loading pending requests...</p>
              </div>

              <div *ngIf="!loadingRequests && pendingRequests.length > 0" class="requests-table">
                <table mat-table [dataSource]="pendingRequests">
                  <!-- Employee Column -->
                  <ng-container matColumnDef="employee">
                    <th mat-header-cell *matHeaderCellDef>Employee</th>
                    <td mat-cell *matCellDef="let request">
                      <div class="employee-info">
                        <strong>{{request.initiatorName}}</strong>
                        <small>{{request.createdAt | date:'short'}}</small>
                      </div>
                    </td>
                  </ng-container>

                  <!-- Request Type Column -->
                  <ng-container matColumnDef="type">
                    <th mat-header-cell *matHeaderCellDef>Type</th>
                    <td mat-cell *matCellDef="let request">
                      <mat-chip [class]="getRequestTypeClass(request.type)">
                        {{getRequestTypeLabel(request.type)}}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <!-- Title Column -->
                  <ng-container matColumnDef="title">
                    <th mat-header-cell *matHeaderCellDef>Request</th>
                    <td mat-cell *matCellDef="let request">
                      <div class="request-title">
                        {{request.title || 'No Title'}}
                      </div>
                    </td>
                  </ng-container>

                  <!-- Priority Column -->
                  <ng-container matColumnDef="priority">
                    <th mat-header-cell *matHeaderCellDef>Priority</th>
                    <td mat-cell *matCellDef="let request">
                      <mat-chip [class]="getPriorityClass(request)">
                        {{getPriorityLabel(request)}}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <!-- Actions Column -->
                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let request">
                      <div class="action-buttons">
                        <button mat-icon-button [routerLink]="['/requests/details', request.id]">
                          <mat-icon>visibility</mat-icon>
                        </button>
                        <button mat-raised-button color="primary" (click)="quickApprove(request)">
                          <mat-icon>check</mat-icon>
                          Approve
                        </button>
                        <button mat-raised-button color="warn" (click)="quickReject(request)">
                          <mat-icon>close</mat-icon>
                          Reject
                        </button>
                      </div>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="pendingColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: pendingColumns;"></tr>
                </table>
              </div>

              <div *ngIf="!loadingRequests && pendingRequests.length === 0" class="no-data">
                <mat-icon>assignment_turned_in</mat-icon>
                <h3>No Team Requests Pending</h3>
                <p>Your team members have no pending requests requiring approval.</p>
                <div class="no-data-actions">
                  <button mat-raised-button routerLink="/requests/approval">
                    <mat-icon>search</mat-icon>
                    View All Requests
                  </button>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Team Performance -->
          <mat-tab label="Team Performance">
            <div class="tab-content">
              <div class="team-stats">
                <div class="stat-card">
                  <h4>Team Request Activity</h4>
                  <div class="team-activity-summary">
                    <div class="activity-metric">
                      <mat-icon>group</mat-icon>
                      <div class="metric-details">
                        <span class="metric-value">{{teamMetrics.totalTeamMembers}}</span>
                        <span class="metric-label">Team Members</span>
                      </div>
                    </div>
                    <div class="activity-metric">
                      <mat-icon>pending</mat-icon>
                      <div class="metric-details">
                        <span class="metric-value">{{teamMetrics.pendingApprovals}}</span>
                        <span class="metric-label">Awaiting Approval</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="stat-card">
                  <h4>Monthly Approval Summary</h4>
                  <div class="approval-breakdown">
                    <div class="breakdown-item">
                      <span class="label">Approved This Month:</span>
                      <span class="value approved">{{teamMetrics.approvedThisMonth}}</span>
                      <mat-progress-bar mode="determinate" [value]="getApprovalPercentage()" color="primary"></mat-progress-bar>
                    </div>
                    <div class="breakdown-item">
                      <span class="label">Rejected This Month:</span>
                      <span class="value rejected">{{teamMetrics.rejectedThisMonth}}</span>
                      <mat-progress-bar mode="determinate" [value]="getRejectionPercentage()" color="warn"></mat-progress-bar>
                    </div>
                    <div class="breakdown-item">
                      <span class="label">Average Processing Time:</span>
                      <span class="value time">{{teamMetrics.averageApprovalTime}}h</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Recent Approvals -->
          <mat-tab label="Recent Approvals">
            <div class="tab-content">
              <div class="tab-header">
                <h3>Your Recent Approval Activity</h3>
                <p>Track your recent decisions and team request processing</p>
              </div>
              <div class="activity-list">
                <div *ngFor="let activity of recentActivities" class="activity-item">
                  <div class="activity-icon" [class]="getActivityIconClass(activity.icon)">
                    <mat-icon>{{activity.icon}}</mat-icon>
                  </div>
                  <div class="activity-content">
                    <p>{{activity.description}}</p>
                    <small>{{activity.timestamp | date:'short'}}</small>
                  </div>
                </div>
              </div>
              <div *ngIf="recentActivities.length === 0" class="no-activity">
                <mat-icon>history</mat-icon>
                <h3>No Recent Activity</h3>
                <p>Your approval activities will appear here once you start processing requests.</p>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </mat-card>
    </div>
  `,
  styles: [`
    .manager-dashboard {
      padding: 1rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .dashboard-header {
      margin-bottom: 2rem;
    }

    .dashboard-header h1 {
      margin: 0;
      color: #333;
    }

    .dashboard-header p {
      margin: 0.5rem 0 0 0;
      color: #666;
    }

    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .metric-card {
      transition: transform 0.2s ease;
    }

    .metric-card:hover {
      transform: translateY(-2px);
    }

    .metric-card.pending {
      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
      color: white;
    }

    .metric-card.team {
      background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
      color: white;
    }

    .metric-card.approved {
      background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
      color: white;
    }

    .metric-card.time {
      background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
      color: white;
    }

    .metric {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .metric-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 255, 255, 0.2);
    }

    .metric-icon mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }

    .metric-info h3 {
      margin: 0;
      font-size: 2rem;
      font-weight: bold;
    }

    .metric-info p {
      margin: 0.25rem 0 0 0;
      font-size: 1rem;
    }

    .content-card {
      margin-top: 1rem;
    }

    .tab-content {
      padding: 1rem;
    }

    .tab-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .requests-table {
      overflow-x: auto;
    }

    .requests-table table {
      width: 100%;
    }

    .employee-info {
      display: flex;
      flex-direction: column;
    }

    .employee-info small {
      color: #666;
      font-size: 0.8rem;
    }

    .request-title {
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .action-buttons {
      display: flex;
      gap: 0.5rem;
      align-items: center;
    }

    .type-leave { background-color: #e3f2fd; color: #1976d2; }
    .type-expense { background-color: #f3e5f5; color: #7b1fa2; }
    .type-training { background-color: #e8f5e8; color: #2e7d32; }
    .type-it { background-color: #fff3e0; color: #ef6c00; }
    .type-profile { background-color: #fce4ec; color: #c2185b; }

    .priority-high { background-color: #ffebee; color: #c62828; }
    .priority-medium { background-color: #fff3e0; color: #ef6c00; }
    .priority-low { background-color: #e8f5e8; color: #2e7d32; }

    .no-data {
      text-align: center;
      padding: 3rem;
      color: #666;
    }

    .no-data mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      margin-bottom: 1rem;
      color: #4caf50;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 3rem;
      color: #666;
    }

    .loading-container p {
      margin-top: 1rem;
      font-size: 1rem;
    }

    .metric-card.loading {
      background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
      color: #666;
    }

    .team-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 2rem;
    }

    .stat-card {
      padding: 1rem;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background-color: #fafafa;
    }

    .stat-card h4 {
      margin: 0 0 1rem 0;
      color: #333;
    }

    .chart-placeholder {
      text-align: center;
      padding: 2rem;
      color: #666;
    }

    .chart-placeholder mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      margin-bottom: 1rem;
    }

    .approval-breakdown {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .breakdown-item {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .breakdown-item .label {
      font-weight: 500;
    }

    .activity-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .activity-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem;
      background-color: #f8f9fa;
      border-radius: 8px;
    }

    .activity-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #2196f3;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .activity-content p {
      margin: 0;
      font-weight: 500;
    }

    .activity-content small {
      color: #666;
    }

    .header-actions {
      margin-top: 1rem;
    }

    .header-info {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #666;
      font-size: 0.9rem;
      margin-top: 0.5rem;
    }

    .header-info mat-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }

    .no-data-actions {
      margin-top: 1.5rem;
    }

    .team-activity-summary {
      display: flex;
      gap: 2rem;
      margin-bottom: 1rem;
    }

    .activity-metric {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem;
      background-color: #f8f9fa;
      border-radius: 8px;
      flex: 1;
    }

    .activity-metric mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      color: #2196f3;
    }

    .metric-details {
      display: flex;
      flex-direction: column;
    }

    .metric-value {
      font-size: 1.5rem;
      font-weight: bold;
      color: #333;
    }

    .metric-label {
      font-size: 0.9rem;
      color: #666;
    }

    .value.approved {
      color: #4caf50;
      font-weight: bold;
    }

    .value.rejected {
      color: #f44336;
      font-weight: bold;
    }

    .value.time {
      color: #ff9800;
      font-weight: bold;
    }

    .activity-icon.approved {
      background-color: #4caf50;
    }

    .activity-icon.rejected {
      background-color: #f44336;
    }

    .activity-icon.pending {
      background-color: #ff9800;
    }

    .no-activity {
      text-align: center;
      padding: 3rem;
      color: #666;
    }

    .no-activity mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      margin-bottom: 1rem;
      color: #ccc;
    }

    @media (max-width: 768px) {
      .metrics-grid {
        grid-template-columns: 1fr;
      }

      .tab-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
      }

      .action-buttons {
        flex-direction: column;
      }

      .team-stats {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class ManagerDashboardComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  // Loading states
  loadingMetrics = true;
  loadingRequests = true;

  // Data properties
  teamMetrics: TeamMetrics = {
    totalTeamMembers: 0,
    pendingApprovals: 0,
    approvedThisMonth: 0,
    rejectedThisMonth: 0,
    averageApprovalTime: 0
  };

  pendingRequests: RequestDto[] = [];
  pendingColumns: string[] = ['employee', 'type', 'title', 'priority', 'actions'];

  recentActivities = [
    {
      icon: 'check_circle',
      description: 'Approved training request from team member',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
    },
    {
      icon: 'pending',
      description: 'New request submitted by team member awaiting review',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4 hours ago
    },
    {
      icon: 'cancel',
      description: 'Rejected leave request - insufficient documentation',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000) // 6 hours ago
    }
  ];

  constructor(
    private readonly requestService: RequestService,
    private readonly authService: AuthService,
    private readonly reportingService: ReportingService
  ) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadDashboardData(): void {
    // Load both pending requests and manager metrics
    this.loadPendingRequests();
    this.loadManagerMetrics();
  }

  loadPendingRequests(): void {
    this.loadingRequests = true;

    const params: PaginationParams = {
      pageNumber: 1,
      pageSize: 10,
      sortBy: 'createdAt',
      sortDirection: 'desc'
    };

    // Use the correct endpoint that returns all requests, then filter for pending ones
    this.requestService.getRequests(params).pipe(
      takeUntil(this.destroy$),
      catchError(error => {
        console.error('Error loading requests:', error);
        // Return mock data as fallback
        return of({
          data: [
            {
              id: '1',
              type: RequestType.Leave,
              initiatorId: 'user1',
              initiatorName: 'John Doe',
              status: RequestStatus.Pending,
              title: 'Vacation Leave - Dec 20-30',
              description: 'Annual vacation leave',
              createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
              requestSteps: []
            },
            {
              id: '2',
              type: RequestType.Expense,
              initiatorId: 'user2',
              initiatorName: 'Jane Smith',
              status: RequestStatus.Pending,
              title: 'Business Trip Expenses',
              description: 'Client meeting expenses',
              createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000),
              requestSteps: []
            }
          ],
          totalCount: 2,
          pageNumber: 1,
          pageSize: 10,
          totalPages: 1,
          hasPreviousPage: false,
          hasNextPage: false
        });
      })
    ).subscribe({
      next: (response) => {
        // Get current manager's ID to filter out their own requests
        const currentUser = this.authService.getCurrentUser();
        const currentUserId = currentUser?.Id || currentUser?.id;

        // Filter for pending requests only (status = 1) and exclude manager's own requests
        this.pendingRequests = response.data.filter(request =>
          request.status === RequestStatus.Pending &&
          request.initiatorId !== currentUserId
        );

        console.log('Loaded requests:', response.data);
        console.log('Current manager ID:', currentUserId);
        console.log('Pending team requests (excluding manager\'s own):', this.pendingRequests);
        this.loadingRequests = false;
      },
      error: (error) => {
        console.error('Error loading requests:', error);
        this.loadingRequests = false;
      }
    });
  }

  loadManagerMetrics(): void {
    this.loadingMetrics = true;

    // Load all requests to calculate metrics
    const params: PaginationParams = {
      pageNumber: 1,
      pageSize: 100, // Get more requests to calculate better metrics
      sortBy: 'createdAt',
      sortDirection: 'desc'
    };

    this.requestService.getRequests(params).pipe(
      takeUntil(this.destroy$),
      catchError(error => {
        console.error('Error loading requests for metrics:', error);
        this.setMockMetrics();
        return of({
          data: [],
          totalCount: 0,
          pageNumber: 1,
          pageSize: 100,
          totalPages: 1,
          hasPreviousPage: false,
          hasNextPage: false
        });
      })
    ).subscribe({
      next: (response) => {
        this.calculateMetricsFromRequests(response.data);
        this.loadingMetrics = false;
      },
      error: (error) => {
        console.error('Error loading requests for metrics:', error);
        this.setMockMetrics();
      }
    });
  }

  private calculateMetricsFromRequests(requests: RequestDto[]): void {
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Filter requests from this month
    const thisMonthRequests = requests.filter(request =>
      new Date(request.createdAt) >= thisMonth
    );

    // Calculate metrics
    const pendingRequests = requests.filter(r => r.status === RequestStatus.Pending);
    const approvedThisMonth = thisMonthRequests.filter(r => r.status === RequestStatus.Approved);
    const rejectedThisMonth = thisMonthRequests.filter(r => r.status === RequestStatus.Rejected);

    // Calculate average approval time (mock calculation for now)
    const completedRequests = requests.filter(r =>
      r.status === RequestStatus.Approved || r.status === RequestStatus.Rejected
    );

    let averageTime = 0;
    if (completedRequests.length > 0) {
      const totalTime = completedRequests.reduce((sum, request) => {
        const created = new Date(request.createdAt);
        const updated = request.updatedAt ? new Date(request.updatedAt) : new Date();
        const diffHours = (updated.getTime() - created.getTime()) / (1000 * 60 * 60);
        return sum + diffHours;
      }, 0);
      averageTime = totalTime / completedRequests.length;
    }

    this.teamMetrics = {
      totalTeamMembers: 8, // This would come from user service in real implementation
      pendingApprovals: pendingRequests.length,
      approvedThisMonth: approvedThisMonth.length,
      rejectedThisMonth: rejectedThisMonth.length,
      averageApprovalTime: Math.round(averageTime * 10) / 10 // Round to 1 decimal
    };

    console.log('Calculated metrics:', this.teamMetrics);
  }

  private setMockMetrics(): void {
    this.teamMetrics = {
      totalTeamMembers: 8,
      pendingApprovals: 5,
      approvedThisMonth: 23,
      rejectedThisMonth: 3,
      averageApprovalTime: 4.2
    };
    this.loadingMetrics = false;
  }

  getRequestTypeLabel(type: RequestType): string {
    switch (type) {
      case RequestType.Leave: return 'Leave';
      case RequestType.Expense: return 'Expense';
      case RequestType.Training: return 'Training';
      case RequestType.ITSupport: return 'IT Support';
      case RequestType.ProfileUpdate: return 'Profile';
      default: return 'Unknown';
    }
  }

  getRequestTypeClass(type: RequestType): string {
    switch (type) {
      case RequestType.Leave: return 'type-leave';
      case RequestType.Expense: return 'type-expense';
      case RequestType.Training: return 'type-training';
      case RequestType.ITSupport: return 'type-it';
      case RequestType.ProfileUpdate: return 'type-profile';
      default: return '';
    }
  }

  getPriorityLabel(request: RequestDto): string {
    // Calculate priority based on age of request
    const daysSinceCreated = Math.floor((Date.now() - new Date(request.createdAt).getTime()) / (1000 * 60 * 60 * 24));
    if (daysSinceCreated > 3) return 'High';
    if (daysSinceCreated > 1) return 'Medium';
    return 'Low';
  }

  getPriorityClass(request: RequestDto): string {
    const priority = this.getPriorityLabel(request);
    switch (priority) {
      case 'High': return 'priority-high';
      case 'Medium': return 'priority-medium';
      case 'Low': return 'priority-low';
      default: return '';
    }
  }

  quickApprove(request: RequestDto): void {
    if (!request.requestSteps || request.requestSteps.length === 0) {
      console.warn('No request steps found for approval');
      return;
    }

    // Find the current pending step (using StepStatus.Pending which is 1)
    const currentStep = request.requestSteps.find(step => step.status === 1);
    if (!currentStep) {
      console.warn('No pending step found for approval');
      return;
    }

    const approvalData: ApproveRejectStepDto = {
      comments: 'Quick approval from manager dashboard'
    };

    this.requestService.approveStep(request.id, currentStep.id, approvalData).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: () => {
        console.log('Request approved successfully');
        // Refresh the pending requests list
        this.loadPendingRequests();
        // Refresh metrics
        this.loadManagerMetrics();
      },
      error: (error) => {
        console.error('Error approving request:', error);
      }
    });
  }

  quickReject(request: RequestDto): void {
    if (!request.requestSteps || request.requestSteps.length === 0) {
      console.warn('No request steps found for rejection');
      return;
    }

    // Find the current pending step (using StepStatus.Pending which is 1)
    const currentStep = request.requestSteps.find(step => step.status === 1);
    if (!currentStep) {
      console.warn('No pending step found for rejection');
      return;
    }

    const rejectionData: ApproveRejectStepDto = {
      comments: 'Quick rejection from manager dashboard'
    };

    this.requestService.rejectStep(request.id, currentStep.id, rejectionData).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: () => {
        console.log('Request rejected successfully');
        // Refresh the pending requests list
        this.loadPendingRequests();
        // Refresh metrics
        this.loadManagerMetrics();
      },
      error: (error) => {
        console.error('Error rejecting request:', error);
      }
    });
  }

  getApprovalPercentage(): number {
    const total = this.teamMetrics.approvedThisMonth + this.teamMetrics.rejectedThisMonth;
    if (total === 0) return 0;
    return (this.teamMetrics.approvedThisMonth / total) * 100;
  }

  getRejectionPercentage(): number {
    const total = this.teamMetrics.approvedThisMonth + this.teamMetrics.rejectedThisMonth;
    if (total === 0) return 0;
    return (this.teamMetrics.rejectedThisMonth / total) * 100;
  }

  getActivityIconClass(icon: string): string {
    switch (icon) {
      case 'check_circle': return 'approved';
      case 'cancel': return 'rejected';
      case 'pending': return 'pending';
      default: return '';
    }
  }
}
